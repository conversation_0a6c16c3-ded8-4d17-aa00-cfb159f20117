const path = require('path');
const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

const defaultConfig = getDefaultConfig(__dirname);
const { assetExts, sourceExts } = defaultConfig.resolver;

const VALID_ORG_EXTENSION = ['mmt'];
const VALID_ORG_EXTENSION_REGEX = new RegExp(
  `(\.(${VALID_ORG_EXTENSION.join('|')})\.(${sourceExts.join('|')}))$`,
  'i',
);

function resolveFileName(context, possibleFileName, platform) {
  try {
    return !!context.resolveRequest(context, possibleFileName, platform)?.filePath;
  } catch (err) {
    return false;
  }
}

/**
 * Metro configuration
 * https://facebook.github.io/metro/docs/configuration
 *
 * @type {import("metro-config").MetroConfig}
 */
const config = {
  resolver: {
    sourceExts: [...sourceExts],
    resolveRequest: (context, moduleName, platform) => {
      try {
        return context.resolveRequest(context, moduleName, platform);
      } catch (e) {
        // do nothing, we check .mmt|.gi extension below
      }
      if (moduleName.search(VALID_ORG_EXTENSION_REGEX) === -1) {
        const possibleFileNames = VALID_ORG_EXTENSION.map((orgExt) => {
          const fileExt = sourceExts.find((ext) => moduleName.endsWith('.' + ext));
          const fileName = fileExt ? moduleName.split('.').slice(0, -1).join('.') : moduleName;

          return fileExt ? `${fileName}.${orgExt}.${fileExt}` : `${fileName}.${orgExt}`;
        }).flat();

        for (const possibleFileName of possibleFileNames) {
          if (resolveFileName(context, possibleFileName, platform)) {
            return context.resolveRequest(context, possibleFileName, platform);
          }
        }
      }

      // Optionally, chain to the standard Metro resolver.
      return context.resolveRequest(context, moduleName, platform);
    },
  },
  transformer: {
    allowOptionalDependencies: true,
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  watchFolders: [
    path.resolve(__dirname, 'node_modules/@ptui/chatbot-ui'),
    path.resolve(__dirname, 'node_modules/@gommt-cabs/rn'),
  ],
};

module.exports = mergeConfig(defaultConfig, config);
