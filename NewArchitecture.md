This document specifies the new architecture going forward for this project
#Project Structure
```
Mobile-mmt-react-native
|-apps
    |-bus
    |-rails
    |-cabs
    |-metro
    |-acme
    |-holidays
    |-postsales
    |....
|-packages
    |-core #all core functionalities(analytics, auth, AB, helpers) used by all the LOBs. This is plan JS, not react or react-native code should be added here
    |-ui #all core components, base themes, color palette
    |-<common helper> # any complex helpers used by mulitple LOBs like Location, LiveTracking, custom hook, etc...
|-src 
    |- Router # this imports all apps and mounts it on the router, LOBs should design their imports such that each routes can be lazily loaded.
    
```
#Best Practices

###Cross Cutting Concerns
Cross cutting concerns are logics which are used by most of your sub-modules. 
Some examples for cross-cutting concerns are:
- Analytics(Omniture, PDT, Tune...)
- Networking(Fetch, Interceptors, GraphQL(experimental)...)
- AB
- Authentication
- Storage(Async Storage)

It is very important to have a clean abstraction on these concerns because if the implementation detail of any these needs to changed in the future, it might involve large refactoring and testing of entire code base.
We can minimize this risk if have an clean abstraction over these concerns.
Example: Imagine if we want to move away from omniture to google-analytics(or PDT), we might have to change 100s of files. But if we have a clear separation between implementation detail(ex:Omniture) and rest of our code, we can easily refactor them.

Clean Abstraction vs Leaky Abstraction
- Clean abstraction does not reveal anything about implementation details. Ex: Abstraction on analytics should not reveal any internals of omniture like eVar,props,pageName,etc...
- Leaky Abstraction accidentally reveals some implementation details, this will result in increased friction when changing the implementation details.  


##LOB best practices
- Follow the abstractions provided by `packages/core`, try not to duplicate the code.
- Have a proper file structure, follow the recommended structure in this document.
- Use Typescript with proper types, avoid `any`. 
- Do not let the components have deal with data manipulation. Example: component should not create a message template by combining server response.
  Move this to data layer. Your data layer should give the response which can be consumed by your components with any modification. 
- Use `SVG` icons for icons, this can reduce size of the APK. This will also benefit your RNW.


#LOB Project Structure
```
    Mobile-mmt-react-native
        |-apps
        |   |-app<ABC> #Ex: Bus,Trains,Cabs...
        |   |   |-Page1 #Ex: Landing
        |   |   |   |-components
        |   |   |   |   |-componentX
        |   |   |   |   |   |-index.js
        |   |   |   |   |-componentY
        |   |   |   |   |   |-index.js
        |   |   |   |-data # this will contain all data fetching,manipulation abstraction
        |   |   |   |   |- api.js
        |   |   |   |   |- cache.js
        |   |   |   |   |- pageAdapter.js # this will map your API response data to data required by components
        |   |   |   |-index.js # export default component here
        |   |   |-Page2
        |   |   |-Page3
        |   |   |-routeConfig.js # Export router config here if you are planning to use React-Navigation
        |   |   |-pageKeys.js # Export all page keys(router keys) here if you are planning to use React-Navigation. 
                              # Page key format "@trips/<verticle>/<page-name>, 
                              #  ex: "@trips/train/bookingDetails" 
        |   |   |-navigation.js # Export routing logic here, abstraction for navigation
        |   |   |-analytics.js # Export analytics logic here, abstraction for analytics

```
