MMT React Native
---
**Project Setup:**
* android: [setup-android.md](setup-android.md)

**IDEs and Plugins**
* IDE (any one of these): 
    * Webstorm
    * Visual Studio Code
    * Atom
* IDE Plugins: 
    * Webstorm (Built in, just jave to enable it for the project <br> `Settings | Languages and 
    Frameworks | JavaScript | Code Quality Tools | ESLint`)
    * VSCode: [ESLint]("https://github.com/Microsoft/vscode-eslint)
    * Atom : [linter-eslint]("https://github.com/AtomLinter/linter-eslint")
* Other Tools:
    * React Devtools [react-devtools]("https://github.com/facebook/react-devtools/tree/master/packages/react-devtools") for inspecting views, 
    props etc.  
    * Gerrit [plugin]("https://plugins.jetbrains.com/plugin/7272-gerrit") for Webstorm for code 
    reviews form IDE (Optional)
    
####Code Style
This Project uses airbnb's JS code styles. Please check [here](https://github.com/airbnb/javascript)</br>
ESLint will show errors when this codestyle is violated

####Coding Conventions and Best Practices
* All React Component files named Capitalized **CamelCase**, others should be just **camelCase**
* Fix all the lint errors before commiting the changes. Run `eslint --fix` for errors which can 
be safely fixed by eslint tools, rest of the errors can be fixed manually.
* Use Pure Components when possible for performance benefits
* Use proper PropTypes, for date Prop use PropTypes.instanceOf(Date) not PropTypes.object .
* Consider Using PropTypes.[ oneOf | arrayOf | shape] etc when its possible and required. Check 
[this](https://reactjs.org/docs/typechecking-with-proptypes.html)  
* Don't inline the styles due its performance hit
* Use async/await instead of promises
* Document all the Native modules
* Prefer using Redux for state management
* Documentation is a must for any common code(Any file excluding ones Routes/**)

**Stack/Libraries**
* [Redux](https://github.com/reactjs/react-redux) for state management
* [React Native Router Flux](https://github.com/aksonov/react-native-router-flux) for routing
* [Fecha](https://github.com/taylorhakes/fecha) for date-time parsing/formatting
* [Lodash](https://lodash.com/docs) for common operations like object/array/string 
manipulation and others
* [React Native Linear Gradient](https://github.com/react-native-community/react-native-linear-gradient) for generating color gradients
* [React Native Vector Icons](https://github.com/oblador/react-native-vector-icons) for font icon
 support

**Project Structure**
- **src**
    - **Assets/** (Static assets like images and fonts)
    - **Common/Components** (Generic Components which can be used by all LOBs)
    - **Helpers** (Common helpers like dateTimeHelpers, data feching, etc which could be used by all
     LOBs)
    - **Native** (Native Modules)
    - **Styles** (Common style guides of colors and fonts)
    - **Routes** (LOBs)
        - ***Cabs***
            - *Landing* (Page)
            - *Listing*
            - *Review*
            - *XYZ*
                - Components/ (Presentational component which are page specific)
                    - XYZStyles.js (Styles specific for the page)
                - XYZContainer.js (redux container)
                - XYZActions.js (Actions(+ Async actions) and Action Creators)
                - XYZReducer.js (Reducer for XYZ Page)
                - index.js (this file exports the XYZContainer)
        - ***Flights***
            - ...
        - ***Hotels***
            - ...
        - ...

####Common issues
* setTimeout/clearTimeout/setInterval doesn't work properly when connected to debugger or hot 
reloading enabled [Issue](https://github.com/facebook/react-native/issues/14639)
* sometimes asynStorage doesn't work properly. [Issue](https://github.com/facebook/react-native/issues/14101)
