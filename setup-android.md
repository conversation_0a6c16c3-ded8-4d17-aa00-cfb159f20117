## Setup React Native for Android

**Clone APP-Android**
```
git clone ssh://<EMAIL>:29418/APP-android && scp -p -P 29418 <EMAIL>:hooks/commit-msg APP-android/.git/hooks/
```
**Go to android root project**
```
cd APP-Android
```
**switch branch**
```
git checkout fb_mobile_cabs_react_native
```
**Add react native module**
```
git submodule add ssh://<EMAIL>:29418/Mobile-mmt-react-native 
```
**Copy commit hook into the submodule**
```
scp -p -P 29418 <EMAIL>:hooks/commit-msg .git/modules/Mobile-mmt-react-native/hooks
```
**Change dir to react native module and install dependencies**
```
cd Mobile-mmt-react-native
git checkout integration
npm install
```
**Run the app**
* Open the project in Android Studio and  perform gradle sync and run the app in device/emulator
* Go to react-native module and start the dev dev server by ```npm start```
* Open Cabs in the app to see the react-native app

**Generating Prebundled APKs**
```
cd Mobile-mmt-react-native
react-native bundle --platform android --dev false --entry-file index.android.js --bundle-output ../mobile/src/main/assets/index.android.bundle --assets-dest ../mobile/src/main/res/
```

**Generating the APK**
<br>
Goto APP-Android
<br>
* Debug
```
./gradlew --parallel --configure-on-demand assembleStandardBasicOptimizedDebug

```
* Release
```
./gradlew --parallel --configure-on-demand assembleStandardBasicOptimizedRelease

```
**Run Flipkart Ultra**
<br>
The following command will strip out the code not neccessary for Ultra integration and keep only 
the files required files
```
npm run fk-ultra
```
The configuration can be found at
<br>
<b>
ultra-build-integration/bus/build-bus-ultra.js
</b>
