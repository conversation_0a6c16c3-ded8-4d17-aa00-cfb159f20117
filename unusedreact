import json
import os
import subprocess
import sys
import time
from multiprocessing import Process

def read_package_json():
    with open('package.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
        return data.get('dependencies', {}), data.get('devDependencies', {})

def find_unused_dependencies(dependencies):
    used_dependencies = set()
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.js') or file.endswith('.jsx') or file.endswith('.ts') or file.endswith('.tsx'):
                filepath = os.path.join(root, file)
                with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    for dep in dependencies:
                        if dep in content:
                            used_dependencies.add(dep)
    return set(dependencies) - used_dependencies

def show_loader():
    while True:
        sys.stdout.write('\rSearching for unused dependencies...')
        sys.stdout.flush()
        time.sleep(0.1)

def main():
    dependencies, dev_dependencies = read_package_json()
    all_dependencies = set(list(dependencies.keys()) + list(dev_dependencies.keys()))

    loader_process = Process(target=show_loader)
    loader_process.start()

    try:
        unused_dependencies = find_unused_dependencies(all_dependencies)
    finally:
        loader_process.terminate()
        loader_process.join()

    with open('output.txt', 'w', encoding='utf-8') as output_file:
        output_file.write("Unused Dependencies:\n")
        for dep in unused_dependencies:
            output_file.write(dep + "\n")

    print("\nResults saved in output.txt")

if __name__ == "__main__":
    main()
