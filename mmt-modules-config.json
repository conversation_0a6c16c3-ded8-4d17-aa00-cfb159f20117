{"core": {"path": ["./src/Assets", "./src/AppState", "./src/react-native", "./src/testingConfig", "./src/Common", "./src/adsConfig", "./src/Native", "./src/Styles", "./src/Helpers", "./packages/ui"]}, "Acme": {"dependsOn": ["core"], "path": ["./src/Routes/Acme"]}, "Bus": {"dependsOn": ["core"], "path": ["./src/Routes/Bus", "./apps/bus"]}, "Cabs": {"dependsOn": ["core"], "path": ["./src/Routes/Cabs", "./apps/crosslob/cabs"]}, "Cabs-Community": {"dependsOn": ["core"], "path": ["./src/Routes/Cabs-Community"]}, "Holidays": {"dependsOn": ["core"], "path": ["./src/Routes/Holidays"]}, "Hubble": {"dependsOn": ["core"], "path": ["./src/Routes/Hubble"]}, "Metro": {"dependsOn": ["core"], "path": ["./src/Routes/Metro"]}, "postsales": {"dependsOn": ["core", "Flights"], "path": ["./src/Routes/postsales", "./src/Routes/Acme/Web", "./src/Routes/Flights", "./src/Routes/Hotels"]}, "Rails": {"dependsOn": ["core"], "path": ["./src/Routes/Rails", "./src/Routes/Bus/BusTrainCrossSell"]}, "Visa": {"dependsOn": ["core"], "path": ["./src/Routes/Visa"]}, "common": {"dependsOn": ["core"], "path": ["./src/Routes/Wallet", "./src/Routes/SpinWin", "./src/Routes/Spider", "./src/Routes/ReferAdminMyBiz", "./src/Routes/travelMall"]}}