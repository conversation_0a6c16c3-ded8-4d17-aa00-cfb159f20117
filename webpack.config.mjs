import path from 'node:path';
import { fileURLToPath } from 'node:url';
import * as Repack from '@callstack/repack';
import TerserPlugin from 'terser-webpack-plugin';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Webpack configuration enhanced with Re.Pack defaults for React Native.
 * 
 * This configuration supports:
 * - React Native 0.78.2
 * - Monorepo structure (apps/, packages/, libraries/)
 * - Babel module aliases from babel.config.js
 * - TypeScript support
 * - Production optimizations
 * 
 * Learn about Webpack configuration: https://webpack.js.org/configuration/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */

export default (env) => {
  const { mode = 'development', platform = 'android' } = env;
  const isDev = mode === 'development';
  const isProd = mode === 'production';

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    
    // Output configuration with platform-specific paths
    output: {
      path: path.resolve(__dirname, 'build/generated', platform),
      filename: isDev ? 'index.bundle' : '[name].bundle',
      chunkFilename: '[name].chunk.bundle',
      publicPath: '/',
      clean: true,
    },

    // Development server configuration
    devServer: isDev ? {
      host: 'localhost',
      port: 8086, // Match your existing Metro port
      hot: true,
      server: 'http',
      static: {
        directory: path.join(__dirname, 'build/generated', platform),
      },
    } : false,

    // Module resolution
    resolve: {
      ...Repack.getResolveOptions(),
      extensions: [
        `.[platform].ts`,
        `.[platform].tsx`, 
        `.[platform].js`,
        `.[platform].jsx`,
        '.ts',
        '.tsx',
        '.js',
        '.jsx',
        '.json',
        '.mmt.ts',
        '.mmt.tsx',
        '.mmt.js',
        '.mmt.jsx',
      ],
      alias: {
        // Match aliases from babel.config.js
        'src': path.resolve(__dirname, 'src'),
        'react-native-pager-view': 'mmt-react-native-pager-view',
        'react-native-orientation': 'mmt-react-native-orientation',
        '@react-native-firebase': 'gommt-react-native-firebase',
        '@mmt/hubble-design-system': '@mmt/hubble/hubble-design-system',
        
        // Add monorepo aliases
        '@mmt/apps': path.resolve(__dirname, 'apps'),
        '@mmt/packages': path.resolve(__dirname, 'packages'),
        '@mmt/libraries': path.resolve(__dirname, 'libraries'),
        '@mmt/legacy-commons': path.resolve(__dirname, 'packages/legacy-commons'),
        
        // React Native specific aliases
        'react-native$': path.resolve(__dirname, 'src/react-native'),
      },
    },

    // Module rules for different file types
    module: {
      rules: [
        // JavaScript/TypeScript files
        {
          test: /\.[jt]sx?$/,
          include: [
            path.resolve(__dirname, 'src'),
            path.resolve(__dirname, 'apps'),
            path.resolve(__dirname, 'packages'),
            path.resolve(__dirname, 'libraries'),
            path.resolve(__dirname, 'index.js'),
            path.resolve(__dirname, 'index.android.js'),
            path.resolve(__dirname, 'index.ios.js'),
            // Include node_modules that need transpilation
            /node_modules\/@mmt/,
            /node_modules\/@Frontend_Ui_Lib_App/,
            /node_modules\/@RN_UI_Lib/,
            /node_modules\/@core_app_/,
            /node_modules\/@bus-fe-commons/,
            /node_modules\/@rails-fe-one/,
            /node_modules\/@gommt-cabs/,
            /node_modules\/@forex/,
            /node_modules\/@growth/,
            /node_modules\/@ptui/,
            /node_modules\/@travelplex/,
            /node_modules\/@trip-money/,
            /node_modules\/MMT-UI/,
            /node_modules\/mobile-/,
            /node_modules\/core-/,
            /node_modules\/community/,
            /node_modules\/themeprovider/,
            /node_modules\/ad-react-wrapper/,
          ],
          use: {
            loader: 'babel-loader',
            options: {
              cacheDirectory: true,
              // Use the existing babel.config.js
              configFile: path.resolve(__dirname, 'babel.config.js'),
            },
          },
        },
        
        // Asset rules from Re.Pack
        ...Repack.getAssetTransformRules(),
      ],
    },

    // Plugins
    plugins: [
      new Repack.RepackPlugin({
        context: __dirname,
        mode,
        platform,
        devServer: isDev ? {
          enabled: true,
          port: 8086,
        } : undefined,
      }),
    ],

    // Optimization
    optimization: {
      minimize: isProd,
      minimizer: isProd ? [
        new TerserPlugin({
          terserOptions: {
            compress: {
              drop_console: true,
              drop_debugger: true,
            },
            mangle: {
              keep_fnames: true,
            },
          },
        }),
      ] : [],
      
      // Code splitting configuration - disabled for now to avoid bundle conflicts
      splitChunks: false,
    },

    // Performance hints
    performance: {
      hints: isProd ? 'warning' : false,
      maxAssetSize: 1024 * 1024, // 1MB
      maxEntrypointSize: 1024 * 1024, // 1MB
    },

    // Development tools
    devtool: isDev ? 'cheap-module-source-map' : 'source-map',

    // Stats configuration
    stats: {
      colors: true,
      modules: true,
      children: true,
      chunks: true,
      chunkModules: true,
      errors: true,
      warnings: true,
    },

    // Cache configuration for faster builds
    cache: {
      type: 'filesystem',
      cacheDirectory: path.resolve(__dirname, 'node_modules/.cache/webpack'),
    },

    // Experiments
    experiments: {
      lazyCompilation: isDev,
    },
  };
};
