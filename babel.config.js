module.exports = {
  presets: ['module:@react-native/babel-preset'],
  plugins: [
    [
      'module-resolver',
      {
        extensions: ['.js', '.ts', '.tsx', '.android.js', '.ios.js'],
        alias: {
          src: './src',
          'react-native-pager-view': 'mmt-react-native-pager-view',
          'react-native-orientation': 'mmt-react-native-orientation',
          '@react-native-firebase': 'gommt-react-native-firebase',
          '@mmt/hubble-design-system': '@mmt/hubble/hubble-design-system',
        },
        root: ['./'],
      },
    ],
    ['module:react-native-dotenv'],
    [
      './libraries/build-info-plugin',
      {
        pkgName: '@mmt/build-info',
      },
    ],
    'react-native-reanimated/plugin',
    // ['@babel/plugin-transform-private-methods', { loose: true }],
  ],
  env: {
    production: {
      plugins: ['transform-remove-console'],
    },
  },
};
