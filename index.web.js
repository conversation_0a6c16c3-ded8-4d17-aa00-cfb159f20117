import React from 'react';
// import {Sentry} from 'react-native-sentry'; //"react-native-sentry": "^0.37.0",
import {AppRegistry} from 'react-native';
import AppContainer from './src/AppContainer';
import {JS_VERSION} from '@mmt/legacy-commons/Common/utils/OmnitureTrackerUtils';

const App = props => <AppContainer {...props} />;

if (!__DEV__) {
  // Commenting out Sentry config as bugs.goibibo.com is going down. Needed new public sentry DSN. Not sure where to get them.
  // Sentry.config('https://68abe62e0e204c9b868a86cf6f54b9bc:<EMAIL>/122', {disableNativeIntegration: true})
  //   .install();
  // export an extra context
  // Sentry.setExtraContext({
  //   JS_VERSION
  // });
}
AppRegistry.registerComponent('MmtReactNative', () => App);
