{
  "compilerOptions": {
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "jsx": "react",
    "lib": [
      "dom",
      "ES6",
      "ES2020"
    ],
    "types": [
    ],
    "module": "CommonJS",
    "moduleResolution": "node",
    "noEmit": true,
    "strict": true,
    "target": "esnext",
    "baseUrl": "./",
    "typeRoots": [
      "./types"
    ],
    "paths": {
      "src": [
        "./src"
      ],
      "@mmt/*": [
        "./apps/*",
        "./packages/*",
      ],
    },
    "moduleSuffixes": [
      ".ios",
      ".android",
      ""
    ]
  },
  "exclude": [
    "babel.config.js",
    "metro.config.js",
    "jest.config.js",
    "node_modules",
  ]
}
