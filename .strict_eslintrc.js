/**
 * How it works?
 *  This is a strict eslint config which is applied for only whitelisted lob,
 *  For non whitelisted lob will use the .eslintrc config.
 *  To apply this config for your lob please check lint-staged.config.js.
 *  ONLY FOR MODIFIED LINES THIS ESLINT CHECKS WILL HAPPEN USING eslint-plugin-diff
 */

module.exports = {
  extends: [
    'eslint:recommended',
    '@react-native',
    'plugin:sonarjs/recommended',
    'problems',
    'plugin:promise/recommended',
    'plugin:diff/diff',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['prettier', '@typescript-eslint', 'react-native', 'sonarjs', '@mmt'],
  rules: {
    'prettier/prettier': 'error',
    curly: ['error', 'all'],
    'max-len': [
      'error',
      {
        code: 100,
        ignoreUrls: true,
        ignoreComments: true,
        ignoreTrailingComments: true,
        ignoreStrings: true,
        ignoreTemplateLiterals: true,
      },
    ],
    'no-confusing-arrow': 'warn',
    'no-unused-vars': 'off',
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    'no-unreachable': 'error',
    'no-dupe-else-if': 'error',
    'no-cond-assign': 'error',
    'react-native/no-unused-styles': 'error',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'error',
    'react-native/no-single-element-style-arrays': 'error',
    'react-native/no-raw-text': 'error',
    'no-duplicate-imports': 'error',
    yoda: 'error',
    'no-unused-private-class-members': 'off',
    'no-use-before-define': 'warn',
    'sonarjs/cognitive-complexity': 'warn',
  },
  overrides: [
    {
      files: ['**/*.styles.ts'],
      rules: {
        'sonarjs/no-duplicate-string': 'off',
      },
    },
  ],
  globals: {
    it: true,
    expect: true,
    element: true,
    describe: true,
    xdescribe: true,
    by: true,
    device: true,
    beforeAll: true,
    beforeEach: true,
    afterAll: true,
    jest: true,
    jasmine: true,
    performance: true,
    AbortController: true,
  },
  ignorePatterns: ['apps/bus/**/*.query.type.ts','apps/bus/src/schema.type.ts','apps/bus/src/constants/BusQueryFragments.type.ts'],
};
