import * as ReactNative from 'react-native';
import { jest } from '@jest/globals';
// import '@testing-library/react-native/extend-expect';

global.isUnderTest = true;

// import mocks from node_modules library

global.ignoreDatePickerWarning = true;

require('jest-fetch-mock').enableMocks();

// async storage mock
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock'),
);

import 'react-native-gesture-handler/jestSetup';

require('react-native-reanimated').setUpTests();
// jest.mock('react-native-reanimated', () => {
//   const Reanimated = require('react-native-reanimated/mock');

//   // The mock for `call` immediately calls the callback which is incorrect
//   // So we override it with a no-op
//   Reanimated.default.call = () => {};

//   return Reanimated;
// });

jest.mock('react-native-blob-util', () => {
  return {
    fs: {
      dirs: {
        DocumentDir: '/mock/document/dir',
        CacheDir: '/mock/cache/dir',
        // Add other directories as needed
      },
      writeFile: jest.fn(() => Promise.resolve()),
      readFile: jest.fn(() => Promise.resolve('mocked file content')),
      exists: jest.fn(() => Promise.resolve(true)),
      unlink: jest.fn(() => Promise.resolve()),
      // Mock other fs methods you use
    },
    fetch: jest.fn(() =>
      Promise.resolve({
        data: 'mocked fetch data',
        // Mock other fetch properties like status, headers, etc.
      }),
    ),
  };
});

// Silence the warning: Animated: `useNativeDriver` is not supported because the native animated module is missing
jest.mock('react-native/src/private/animated/NativeAnimatedHelper.js');

jest.mock('react-native-screens', () => ({
  enableScreens: () => jest.fn(),
  screensEnabled: () => false,
}));

global.reduxProviders = {
  default: () => ({
    reducer: {
      testReducer: (state = {}, action) => state,
    },
    resetReducer: (reducer) => (state, action) => {
      return reducer(state, action);
    },
  }),
};

jest.mock('packages/core/native/PerfLogger/screenProfiler', () => ({
  screenProfiler: (component) => component,
  useScreenProfiler: () => ({
    updateState: jest.fn(),
    startNextScreenTimer: jest.fn(),
  }),
}));

jest.mock('@mmt/legacy-commons/Native/OmnitureModule', () => ({
  deviceInfo: () =>
    jest.fn(() => ({
      dvc_os_ver: '14.0',
      dvc_type: 'Test',
      dvc_app_ver: '14.0',
      dvc_did: '14.0',
      dvc_conn_type: 'Mock',
      dvc_manuf: 'Test',
      dvc_mdl: 'Test',
    })),
}));

const PLATFORM_VAL = 'android';

jest.doMock('react-native', () => {
  // Extend ReactNative
  return Object.setPrototypeOf(
    {
      Image: {
        ...ReactNative.Image,
        propTypes: {}, // latest RN version does not have propTypes due to which test cases fails
      },
      Text: {
        ...ReactNative.Text,
        propTypes: {}, // latest RN version does not have propTypes due to which test cases fails
      },
      ViewPropTypes: {}, // latest RN version does not have ViewPropTypes due to which test cases fails
      Platform: {
        ...ReactNative.Platform,
        OS: PLATFORM_VAL,
        Version: 123,
        isTesting: true,
        select: (objs) => objs[PLATFORM_VAL] || objs.default || null,
      },
      NativeModules: {
        ...ReactNative.NativeModules,
        RNAppModule: {
          addListener: jest.fn(),
          removeListeners: jest.fn(),
          eventsAddListener: jest.fn(),
          eventsNotifyReady: jest.fn(),
        },
        RNGoogleMobileAdsModule: {
          addListener: jest.fn(),
          removeListeners: jest.fn(),
          eventsAddListener: jest.fn(),
          eventsNotifyReady: jest.fn(),
        },
        RNGoogleMobileAdsInterstitialModule: {
          interstitialLoad: jest.fn(),
        },
        RNGoogleMobileAdsRewardedModule: {},
        RNGoogleMobileAdsConsentModule: {},
        AbConfigModule: {
          getAbConfig: jest.fn(async () => ({})),
          getPokusConfig: jest.fn(async () => ({})),
        },
        ViewControllerModule: {
          popViewController: jest.fn(),
          thankyouDismiss: jest.fn(),
          dismissPresentedViewController: jest.fn(),
        },
        NetworkModule: {
          getHeaders: jest.fn(() => ({
            mcId: 'test-mcId',
          })),
          getSensorData: jest.fn(() => ({})),
          isNetworkAvailable: jest.fn(() => true),
          getAppLaunchCount: jest.fn(() => 1),
        },
        HolidayModule: {
          getAppLaunchCount: jest.fn(() => 1),
        },
        PaymentModule: {
          openPaymentPage: jest.fn(),
        },
        UserSessionModule: {
          getUserDetails: () =>
            '{"addressId":"188","cupAge":"-1","completionScore":"85","cupCrdt":1534406625000,"cupDob":"************","domainType":"INDIA","cupEmailid":"<EMAIL>","emailVerified":true,"cupFname":"Aditya","frequentFlyers":[],"cupGender":"MALE","homeLocation":{"cityCode":"CTBLR","cityName":"Bangalore","countryCode":"IN","countryName":"India","status":"ACTIVE","type":"city"},"isCorporate":false,"isEncryptionMigrated":true,"isHost":false,"isLoggedIn":true,"cupLname":"Tata","cupUpdt":0,"loginType":"MMT","miscFields":{},"mmtAuth":"MAT101bf92ed6692394b71c298cf23baf944483644439fd59666988a4347ebc6487bfa5fc2c8c45ac5a271fb91e16b4e12a0P","mobileContactNumberList":[],"cupProfileType":"PERSONAL","state":"Karnataka","cupTitle":"Mr","travellerDocuments":[],"uuid":"U6MOGY301NL","verifiedMobileNumber":[{"countryCode":"91","mobileNumber":"**********"}]}',
          getDeviceDetails: jest.fn(),
          isUserLoggedIn: jest.fn(() => true),
          openGDPRBlockerScreen: jest.fn(),
          showGSTNWidget: jest.fn(),
          shouldRefetchUserDataAndroid: jest.fn(),
          isCorporateUser: jest.fn(),
          isUserLoadedCompletely: jest.fn(() => true),
        },
        GenericModule: {
          getDeviceInfo: () => ({
            androidVersion: 'test',
            phoneNumber: 'test',
            accountEmailId: 'test',
          }),
        },
        GenericTrackerModule: {
          trackFirebase: jest.fn(),
          trackTune: jest.fn(),
        },
        LocationHelperModule: {
          fetchCurrentLocation: () => ({ lat: 0, lng: 0 }),
          isLocationServicesEnabled: jest.fn(() => true),
        },
      },
      TurboModuleRegistry: {
        get: () => {
          return {
            initialize: jest.fn(),
            setRequestConfiguration: jest.fn(),
            openAdInspector: jest.fn(),
            openDebugMenu: jest.fn(),
          };
        },
        getEnforcing: () => {
          return {
            initialize: jest.fn(),
            setRequestConfiguration: jest.fn(),
            openAdInspector: jest.fn(),
            openDebugMenu: jest.fn(),
          };
        },
      },
      Linking: {
        ...jest.requireActual('react-native').Linking,
        removeEventListener: jest.fn(),
      },
      PermissionsAndroid: {
        ...jest.requireActual('react-native').PermissionsAndroid,
        check: async () => true,
      },
    },
    ReactNative,
  );
});

function monkeyPatchConsole() {
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  console.error = (...args) => {
    const msg = args[0];
    if (
      typeof msg === 'string' &&
      (msg.includes('will be removed from React Native') ||
        msg.includes('Each child in a list should have a unique "key" prop'))
    ) {
      return;
    }
    // originalConsoleError(...args);
  };
  console.warn = (...args) => {
    const msg = args[0];
    if (
      typeof msg === 'string' &&
      msg.includes('has been renamed, and is not recommended for use')
    ) {
      return;
    }
    // originalConsoleWarn(...args);
  };
}

monkeyPatchConsole();

// CustomModal accidentally ships react-dom, causing some tests to fail
jest.mock('@RN_UI_Lib/CustomModal/lib/Portal', () => require('react-native').View);
jest.mock('@RN_UI_Lib/CustomModal', () => require('@RN_UI_Lib/CustomModal/lib/CustomModal'));
jest.mock('react-native-keyboard-aware-scroll-view', () => {
  const KeyboardAwareScrollView = require('react-native').ScrollView;
  return { KeyboardAwareScrollView };
});
