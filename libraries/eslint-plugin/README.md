# eslint-plugin-mmt-rn

Enforce checks for mmt-rn

## Installation

You'll first need to install [ESLint](https://eslint.org/):

```sh
npm i eslint --save-dev
```

Next, install `eslint-plugin-mmt-rn`:

```sh
npm install eslint-plugin-mmt-rn --save-dev
```

## Usage

Add `mmt-rn` to the plugins section of your `.eslintrc` configuration file. You can omit the `eslint-plugin-` prefix:

```json
{
    "plugins": [
        "mmt-rn"
    ]
}
```


Then configure the rules you want to use under the rules section.

```json
{
    "rules": {
        "mmt-rn/rule-name": 2
    }
}
```

## Supported Rules

* Fill in provided rules here


