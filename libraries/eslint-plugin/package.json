{"name": "@mmt/eslint-plugin", "version": "0.0.1-alpha.0", "description": "Enforce checks for mmt-rn", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": "mmt-rn", "main": "./lib/index.js", "exports": "./lib/index.js", "scripts": {"lint": "eslint .", "test": "mocha tests --recursive"}, "dependencies": {"eslint-plugin": "^1.0.1", "requireindex": "^1.2.0"}, "devDependencies": {"eslint": "^8.19.0", "eslint-plugin-eslint-plugin": "^5.0.0", "eslint-plugin-node": "^11.1.0", "mocha": "^10.0.0"}, "peerDependencies": {"eslint": ">=7"}, "license": "ISC"}