const { execSync } = require('child_process');

function checkIsOnReleaseBranch() {
  const result = execSync('git rev-parse --abbrev-ref HEAD').toString();
  return result.trim() === 'release';
}

function getGitHash() {
  const result = execSync('git rev-parse --short HEAD').toString();
  return result.trim();
}

const isOnReleaseBranch = checkIsOnReleaseBranch();
const gitHash = isOnReleaseBranch ? getGitHash() : `${getGitHash()}--DEV`;

module.exports = function (data) {
  const t = data.types;

  return {
    visitor: {
      ImportDeclaration: function (path, state) {
        const options = state.opts;

        if (path.node.source.value === options.pkgName) {
          const buildInfo = { gitHash };

          path.node.specifiers.forEach(function (specifier, idx) {
            if (specifier.type === 'ImportDefaultSpecifier') {
              throw path
                .get('specifiers')
                [idx].buildCodeFrameError('Import dotenv as default is not supported.');
            }
            const importedId = specifier.imported.name;
            const localId = specifier.local.name;
            if (!buildInfo.hasOwnProperty(importedId)) {
              throw path
                .get('specifiers')
                [idx].buildCodeFrameError(
                  'Unable to import build-info variable "' + importedId + '"',
                );
            }

            const binding = path.scope.getBinding(localId);
            binding.referencePaths.forEach(function (refPath) {
              refPath.replaceWith(t.valueToNode(buildInfo[importedId]));
            });
          });

          path.remove();
        }
      },
    },
  };
};
