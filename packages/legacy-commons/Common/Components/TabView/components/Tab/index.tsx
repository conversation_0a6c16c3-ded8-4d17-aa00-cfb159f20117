import React, { FC, useCallback, useMemo } from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';

import LinearGradient from 'react-native-linear-gradient';
import { Placeholder, PlaceholderContainer } from '@mmt/legacy-commons/ShimmerV2Wrapper';

import OfferIc from '@mmt/legacy-assets/src/Bus/CrossSell/offer.png';
import TouchableRipple from '../../../TouchableRipple';

import { Props } from './types';
import { colors, fonts } from '../../../../../Styles/globalStyles';

const placeholderBg = '#F1F1F1';
const dummyLoaderPromise = new Promise(() => { });

export const Tab: FC<Props> = ({
  testId,
  index,
  style,
  activateTab,
  data: { icon, offer, status, onPress, heading, isLoading, subHeading, isPressDisabled, headingStyle, subHeadingStyle },
}) => {
  const isActive = useMemo(() => status === 'active', [status]);

  const onPressHandler = useCallback(() => {
    activateTab(index);
    onPress && onPress();
  }, [index, onPress, activateTab]);

  return (
    <TouchableRipple disabled={isPressDisabled} onPress={onPressHandler} style={styles.flex1} testID={`${testId}${index}`}>
      <View style={[styles.container, ...(isActive ? [styles.activeBg] : []), style]}>
        <View style={[styles.row, styles.detailsView]}>
          {icon &&
            <Image source={icon.source[status]} style={[styles.icon, icon.style]} />
          }

          <View>
            <View style={styles.row}>
              <Text numberOfLines={1} ellipsizeMode="tail" style={[styles.heading, styles[`${status}Text`], headingStyle]}>{heading}</Text>

              {offer ? (
                <LinearGradient
                  end={{ x: 1.0, y: 0.0 }}
                  start={{ x: 0.0, y: 1.0 }}
                  style={styles.offerGradient}
                  colors={['#B975E8', '#5937B9']}
                >
                  <View style={styles.offerIcContainer}>
                    <Image source={OfferIc} style={styles.offerIc} />
                  </View>
                  <Text style={styles.offer}>{offer}</Text>
                </LinearGradient>
              ) : null}
            </View>

            {isLoading && (
              <PlaceholderContainer
                delay={100}
                duration={1000}
                animatedComponent={
                  <LinearGradient
                    style={styles.flex1}
                    end={{ x: 0.1, y: 0.1 }}
                    start={{ x: 1.0, y: 0.0 }}
                    colors={[placeholderBg, '#f3f3f3', '#d3d3d3', '#f5f5f5']}
                  />
                }
                style={styles.placeholderContainer}
              >
                <Placeholder replace style={styles.placeholder} loader={dummyLoaderPromise} />
              </PlaceholderContainer>
            )}

            {subHeading ? (
              <Text style={[styles.subHeading, styles[`${status}Text`], subHeadingStyle]}>{subHeading}</Text>
            ) : null}
          </View>
        </View>

        <View style={[styles.indicator, ...(isActive ? [styles.azureBg] : [styles.whiteBg])]} />
      </View>
    </TouchableRipple>
  );
};

const styles = StyleSheet.create({
  icon: {
    marginLeft: 10,
  },
  flex1: {
    flex: 1,
  },
  detailsView: {
    paddingTop: 6,
    paddingLeft: 20,
    paddingBottom: 8,
    alignItems: 'center',
    height: 56,
  },
  placeholderContainer: {
    flex: 1,
    width: '100%',
    flexDirection: 'row',
  },
  placeholder: {
    width: 80,
    height: 12,
    borderRadius: 4,
    marginTop: 3,
    backgroundColor: placeholderBg,
  },
  activeText: {
    color: '#282828',
  },
  inactiveText: {
    color: '#4a4a4a',
  },
  disabledText: {
    color: '#4a4a4a30',
  },
  row: {
    flexDirection: 'row',
    width: 150
    // flex: 1,
  },
  subHeading: {
    fontSize: 12,
    marginTop: 3,
    fontFamily: fonts.bold,
  },
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  indicator: {
    height: 4,
    backgroundColor: colors.white,
  },
  activeBg: {
    backgroundColor: '#F2F9FF',
  },
  azureBg: {
    backgroundColor: colors.azure,
  },
  whiteBg: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderColor: colors.lightGray,
  },
  heading: {
    fontSize: 14,
    fontFamily: fonts.black,
    color: colors.defaultTextColor,
  },
  offer: {
    fontSize: 9,
    color: colors.white,
    fontFamily: fonts.medium,
    alignSelf: 'center',
  },
  offerIc: {
    width: 10,
    height: 10,
    marginRight: 5,
  },
  offerIcContainer: {
    paddingBottom: 1,
    alignSelf: 'center',
  },
  offerGradient: {
    marginLeft: 4,
    borderRadius: 10,
    paddingVertical: 2,
    flexDirection: 'row',
    paddingHorizontal: 6,
    justifyContent: 'center',
  },
});
