const fs = require('fs');
const micromatch = require('micromatch');

//Please add your lob if you need to enable eslint on your lob files
const includedFiles = 'apps/{rails,rails-shared,bus,bus-shared}/**/*.{ts,tsx,js}';

//This function check if it is a merge commit
function getMergingBranchRevision() {
  try {
    return fs.readFileSync('.git/MERGE_HEAD', { encoding: 'utf-8' });
  } catch (e) {
    return null;
  }
}

/**
 * How it works?
 *  The eslint will run only on MODIFIED LINES using eslint-plugin-diff plugin.
 *  When it is merge commit, then eslint verification is skipped.
 *  Uses .strict_eslintrc.js eslint config
 *  Blocks the commit till verification is success.
 */
module.exports = async function (allStagedFiles) {
  let mergingBranchRevision = getMergingBranchRevision();
  if (mergingBranchRevision) {
    return 'echo MERGE commit skipping eslint verification';
  }
  //Getting all staged files for lint enabled lobs.
  const eslintFiles = micromatch(allStagedFiles, `${process.cwd()}/${includedFiles}`);

  //Adding commands
  const commands = [];

  //If staged files found then running eslint plugin.
  if (eslintFiles.length) {
    commands.push(`eslint -c .strict_eslintrc.js --fix ${eslintFiles.join(' ').trim()}`);
  }

  // adding cabs verify commands
  commands.push(...addCabsLintCommands(allStagedFiles));

  return commands;
};

function addCabsLintCommands(allStagedFiles) {
  const eslintFiles = micromatch(allStagedFiles, `${process.cwd()}/apps/cabs/**/*.{ts,tsx,js}`);
  const commands = [];

  if (eslintFiles.length) {
    // tsc
    commands.push('npm run cabs-tsc');
    // prettier
    commands.push(
      `prettier --no-editorconfig --config ${process.cwd()}/apps/cabs/.prettierrc --write ${eslintFiles
        .join(' ')
        .trim()}`,
    );
    // eslint
    commands.push('npm run cabs-lint-check');

    // TODO- Add in verify job once cabs-test is stable
    commands.push('npm run cabs-test');
  }

  return commands;
}
