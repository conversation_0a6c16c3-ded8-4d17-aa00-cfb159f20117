{"compilerOptions": {"allowJs": false, "checkJs": false, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "jsx": "react", "lib": ["ES6", "dom"], "types": ["jest"], "module": "CommonJS", "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext", "baseUrl": "./", "typeRoots": ["./types"], "paths": {"src": ["./apps", "./packages"]}}, "exclude": ["babel.config.js", "metro.config.js", "jest.config.js", "node_modules", "src"]}