module.exports = {
  preset: 'react-native',
  setupFiles: ['<rootDir>/setupJest.js'],
  setupFilesAfterEnv: ['@testing-library/jest-native/extend-expect'],
  modulePaths: ['<rootDir>/apps/**', '<rootDir>/packages/**'],
  moduleDirectories: ['node_modules', 'src'],
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@travelplex|@react-native|react-native-.*|@sentry|@react-navigation|@react-native-community|@platform|@Frontend_Ui_Lib_App|@RN_UI_Lib|themeprovider|community|uuid|@gommt-cabs)/)',
  ],
  testPathIgnorePatterns: ['/node_modules/@gommt-cabs/rn/*.gi.js'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'mmt.js', 'js.map'],
  moduleNameMapper: {
    '(^.+)\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$': [
      '$1.$2',
      '$1@2x.$2',
      '$1@3x.$2',
    ],
    '@mmt/(.*)': ['<rootDir>/packages/$1', '<rootDir>/apps/$1', '<rootDir>/libraries/$1'],
    'packages/(.*)': '<rootDir>/packages/$1',
    'apps/(.*)': '<rootDir>/apps/$1',
  },
  coverageThreshold: {
    global: {
      functions: 80,
      branches: 80,
      lines: 80,
      statements: 80,
    },
  },
  collectCoverage: false,
  collectCoverageFrom: ['!node_modules/**', '!lib/**', '<rootDir>/apps/cabs/**/*.(ts|tsx|js)'],
};
